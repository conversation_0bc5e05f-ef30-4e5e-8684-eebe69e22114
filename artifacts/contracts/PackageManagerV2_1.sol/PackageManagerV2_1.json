{"_format": "hh-sol-artifact-1", "contractName": "PackageManagerV2_1", "sourceName": "contracts/PackageManagerV2_1.sol", "abi": [{"inputs": [{"internalType": "address", "name": "usdt_", "type": "address"}, {"internalType": "address", "name": "share_", "type": "address"}, {"internalType": "address", "name": "lp_", "type": "address"}, {"internalType": "address", "name": "vault_", "type": "address"}, {"internalType": "address", "name": "router_", "type": "address"}, {"internalType": "address", "name": "factory_", "type": "address"}, {"internalType": "address", "name": "treasury_", "type": "address"}, {"internalType": "address", "name": "tax_", "type": "address"}, {"internalType": "address", "name": "admin", "type": "address"}], "stateMutability": "nonpayable", "type": "constructor"}, {"inputs": [], "name": "AccessControlBadConfirmation", "type": "error"}, {"inputs": [{"internalType": "address", "name": "account", "type": "address"}, {"internalType": "bytes32", "name": "neededRole", "type": "bytes32"}], "name": "AccessControlUnauthorizedAccount", "type": "error"}, {"inputs": [], "name": "EnforcedPause", "type": "error"}, {"inputs": [], "name": "ExpectedPause", "type": "error"}, {"inputs": [], "name": "ReentrancyGuardReentrantCall", "type": "error"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}], "name": "SafeERC20FailedOperation", "type": "error"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "uint256", "name": "oldWindow", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "newWindow", "type": "uint256"}], "name": "DeadlineWindowUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "lpTokensBurned", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "liquidityRemoved", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "shareTokenReceived", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "usdtReceived", "type": "uint256"}], "name": "LiquidityRedeemed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "string", "name": "name", "type": "string"}, {"indexed": false, "internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"indexed": false, "internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"indexed": false, "internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"indexed": false, "internalType": "uint64", "name": "cliff", "type": "uint64"}, {"indexed": false, "internalType": "uint64", "name": "duration", "type": "uint64"}, {"indexed": false, "internalType": "uint16", "name": "referralBps", "type": "uint16"}], "name": "PackageAdded", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "uint256", "name": "id", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "active", "type": "bool"}], "name": "PackageToggled", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Paused", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "buyer", "type": "address"}, {"indexed": true, "internalType": "uint256", "name": "packageId", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"indexed": false, "internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "referrer", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "referralReward", "type": "uint256"}], "name": "Purchased", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "user", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "lpAmount", "type": "uint256"}], "name": "Redeemed", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "taxKey", "type": "bytes32"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": true, "internalType": "address", "name": "recipient", "type": "address"}], "name": "TaxApplied", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "oldTreasury", "type": "address"}, {"indexed": true, "internalType": "address", "name": "newTreasury", "type": "address"}], "name": "TreasuryUpdated", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "account", "type": "address"}], "name": "Unpaused", "type": "event"}, {"inputs": [], "name": "ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "PURCHASE_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "REFERRAL_TAX_KEY", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"internalType": "uint64", "name": "cliff", "type": "uint64"}, {"internalType": "uint64", "name": "duration", "type": "uint64"}, {"internalType": "uint16", "name": "referralBps", "type": "uint16"}], "name": "addPackage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "deadlineWindow", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "token", "type": "address"}, {"internalType": "uint256", "name": "amount", "type": "uint256"}], "name": "emergencyRecoverToken", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "factory", "outputs": [{"internalType": "contract IPancakeFactory", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getActivePackageIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "getPackage", "outputs": [{"components": [{"internalType": "string", "name": "name", "type": "string"}, {"internalType": "uint256", "name": "entryUSDT", "type": "uint256"}, {"internalType": "uint16", "name": "exchangeRateBps", "type": "uint16"}, {"internalType": "uint16", "name": "vestBps", "type": "uint16"}, {"internalType": "uint64", "name": "cliff", "type": "uint64"}, {"internalType": "uint64", "name": "duration", "type": "uint64"}, {"internalType": "uint16", "name": "referralBps", "type": "uint16"}, {"internalType": "bool", "name": "active", "type": "bool"}, {"internalType": "bool", "name": "exists", "type": "bool"}], "internalType": "struct PackageManagerV2_1.Package", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPackageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPackageIds", "outputs": [{"internalType": "uint256[]", "name": "", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "owner", "type": "address"}], "name": "getPackagesByOwner", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lpAmount", "type": "uint256"}], "name": "getRedemptionPreview", "outputs": [{"internalType": "uint256", "name": "expectedShare", "type": "uint256"}, {"internalType": "uint256", "name": "expectedUSDT", "type": "uint256"}, {"internalType": "uint256", "name": "liquidityToRemove", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPackageCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPackages", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}, {"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getUserPurchase", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPurchaseCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserPurchases", "outputs": [{"components": [{"internalType": "uint256", "name": "packageId", "type": "uint256"}, {"internalType": "uint256", "name": "usdtAmount", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokens", "type": "uint256"}, {"internalType": "uint256", "name": "vestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "poolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "lpTokens", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}, {"internalType": "uint256", "name": "referralReward", "type": "uint256"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserPurchase[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserRedemptionCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserRedemptions", "outputs": [{"internalType": "uint256[]", "name": "amounts", "type": "uint256[]"}, {"internalType": "uint256[]", "name": "timestamps", "type": "uint256[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "user", "type": "address"}], "name": "getUserStats", "outputs": [{"components": [{"internalType": "uint256", "name": "totalInvested", "type": "uint256"}, {"internalType": "uint256", "name": "totalTokensReceived", "type": "uint256"}, {"internalType": "uint256", "name": "totalVestTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalPoolTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalLPTokens", "type": "uint256"}, {"internalType": "uint256", "name": "totalReferralRewards", "type": "uint256"}, {"internalType": "uint256", "name": "purchaseCount", "type": "uint256"}, {"internalType": "uint256", "name": "redemptionCount", "type": "uint256"}, {"internalType": "uint256", "name": "totalRedemptions", "type": "uint256"}], "internalType": "struct PackageManagerV2_1.UserStats", "name": "", "type": "tuple"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "lpToken", "outputs": [{"internalType": "contract ILPToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "nextPackageId", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "pause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "paused", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}, {"internalType": "address", "name": "referrer", "type": "address"}], "name": "purchase", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lpAmount", "type": "uint256"}], "name": "redeem", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "lpAmount", "type": "uint256"}, {"internalType": "uint256", "name": "amountShareMin", "type": "uint256"}, {"internalType": "uint256", "name": "amountUSDTMin", "type": "uint256"}, {"internalType": "uint256", "name": "deadline", "type": "uint256"}], "name": "redeemWithLiquidityRemoval", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "callerConfirmation", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "router", "outputs": [{"internalType": "contract IPancakeRouter", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "newWindow", "type": "uint256"}], "name": "setDeadlineWindow", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newTreasury", "type": "address"}], "name": "setTreasury", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "shareToken", "outputs": [{"internalType": "contract IShareToken", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "taxManager", "outputs": [{"internalType": "contract ISwapTaxManager", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "id", "type": "uint256"}], "name": "togglePackage", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "treasury", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "unpause", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "usdt", "outputs": [{"internalType": "contract IERC20Decimals", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "vesting<PERSON><PERSON>", "outputs": [{"internalType": "contract IVestingVault", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}