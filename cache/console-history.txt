.exit
pm.interface.getFunction("redeemWithLiquidityRemoval")
pm.interface.getFunction("getRedemptionPreview")
await pm.getRedemptionPreview(ethers.parseEther("1"))
const pm = await ethers.getContractAt("PackageManagerV2_1", "******************************************")
.exit
ethers.formatUnits(pkg0.entryUSDT, 6)
pkg0.name
const pkg0 = await pm.getPackage(0)
await pm.getActivePackageIds()
await pm.getPackageCount()
const pm = await ethers.getContractAt("PackageManagerV2_1", "******************************************")